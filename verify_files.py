#!/usr/bin/env python3
"""
Verify that .int files are correctly organized
"""

import os
import re
from pathlib import Path

def verify_int_file(filepath):
    """Verify a single .int file is correctly organized."""
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check that file ends with exactly one newline
    if not content.endswith('\n'):
        return f"❌ {filepath}: File doesn't end with newline"
    if content.endswith('\n\n'):
        return f"❌ {filepath}: File ends with multiple newlines"
    
    # Parse sections
    lines = content.rstrip().split('\n')
    sections = []
    
    for line in lines:
        section_match = re.match(r'^\[([^\]]+)\]$', line)
        if section_match:
            sections.append(section_match.group(1))
    
    # Check section order
    if not sections:
        return f"✓ {filepath}: No sections found (empty file)"
    
    # Check if LevelInfo0 and LevelSummary exist and are in correct order
    has_levelinfo = 'LevelInfo0' in sections
    has_levelsummary = 'LevelSummary' in sections
    
    if has_levelinfo and has_levelsummary:
        levelinfo_pos = sections.index('LevelInfo0')
        levelsummary_pos = sections.index('LevelSummary')
        
        if levelinfo_pos == 0 and levelsummary_pos == 1:
            return f"✓ {filepath}: Correctly ordered - LevelInfo0 first, LevelSummary second"
        elif levelinfo_pos == 0:
            return f"⚠️ {filepath}: LevelInfo0 first but LevelSummary not second (pos {levelsummary_pos})"
        elif levelsummary_pos == 1:
            return f"❌ {filepath}: LevelSummary second but LevelInfo0 not first (pos {levelinfo_pos})"
        else:
            return f"❌ {filepath}: Neither section in correct position (LevelInfo0: {levelinfo_pos}, LevelSummary: {levelsummary_pos})"
    
    elif has_levelinfo:
        levelinfo_pos = sections.index('LevelInfo0')
        if levelinfo_pos == 0:
            return f"✓ {filepath}: LevelInfo0 first (no LevelSummary section)"
        else:
            return f"❌ {filepath}: LevelInfo0 not first (pos {levelinfo_pos})"
    
    elif has_levelsummary:
        levelsummary_pos = sections.index('LevelSummary')
        if levelsummary_pos == 0:
            return f"⚠️ {filepath}: LevelSummary first (no LevelInfo0 section)"
        else:
            return f"❌ {filepath}: LevelSummary not first and no LevelInfo0 (pos {levelsummary_pos})"
    
    else:
        return f"✓ {filepath}: No LevelInfo0 or LevelSummary sections"

def main():
    """Verify all .int files in the current directory."""
    current_dir = Path('.')
    int_files = list(current_dir.glob('*.int'))
    
    if not int_files:
        print("No .int files found in the current directory.")
        return
    
    print(f"Verifying {len(int_files)} .int files...\n")
    
    results = []
    for int_file in sorted(int_files):
        try:
            result = verify_int_file(int_file)
            results.append(result)
            print(result)
        except Exception as e:
            error_msg = f"❌ {int_file}: Error - {e}"
            results.append(error_msg)
            print(error_msg)
    
    # Summary
    success_count = sum(1 for r in results if r.startswith('✓'))
    warning_count = sum(1 for r in results if r.startswith('⚠️'))
    error_count = sum(1 for r in results if r.startswith('❌'))
    
    print(f"\n=== SUMMARY ===")
    print(f"✓ Correct: {success_count}")
    print(f"⚠️ Warnings: {warning_count}")
    print(f"❌ Errors: {error_count}")
    print(f"Total files: {len(int_files)}")

if __name__ == '__main__':
    main()
