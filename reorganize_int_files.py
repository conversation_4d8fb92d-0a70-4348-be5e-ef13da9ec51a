#!/usr/bin/env python3
"""
Script to reorganize .int files:
1. Ensure [LevelInfo0] section comes first
2. Ensure [LevelSummary] section comes second
3. Keep all other sections unchanged in their relative order
4. Ensure files end with exactly one newline
"""

import os
import re
from pathlib import Path

def reorganize_int_file(filepath):
    """Reorganize a single .int file according to the requirements."""
    print(f"Processing {filepath}...")
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Remove trailing whitespace and ensure we work with clean content
    content = content.rstrip()
    
    # Split content into sections
    sections = []
    current_section = []
    current_section_name = None
    
    lines = content.split('\n')
    
    for line in lines:
        # Check if this line starts a new section
        section_match = re.match(r'^\[([^\]]+)\]$', line)
        if section_match:
            # Save the previous section if it exists
            if current_section_name is not None:
                sections.append((current_section_name, current_section))
            
            # Start new section
            current_section_name = section_match.group(1)
            current_section = [line]
        else:
            # Add line to current section
            if current_section_name is not None:
                current_section.append(line)
            else:
                # Content before any section (shouldn't happen in .int files, but handle it)
                if line.strip():  # Only add non-empty lines
                    sections.append(('_HEADER_', [line]))
    
    # Don't forget the last section
    if current_section_name is not None:
        sections.append((current_section_name, current_section))
    
    # Find LevelInfo0 and LevelSummary sections
    levelinfo_section = None
    levelsummary_section = None
    other_sections = []
    
    for section_name, section_content in sections:
        if section_name == 'LevelInfo0':
            levelinfo_section = section_content
        elif section_name == 'LevelSummary':
            levelsummary_section = section_content
        else:
            other_sections.append((section_name, section_content))
    
    # Rebuild the file content
    new_content_lines = []
    
    # Add LevelInfo0 first (if it exists)
    if levelinfo_section:
        new_content_lines.extend(levelinfo_section)
        new_content_lines.append('')  # Empty line after section
    
    # Add LevelSummary second (if it exists)
    if levelsummary_section:
        new_content_lines.extend(levelsummary_section)
        new_content_lines.append('')  # Empty line after section
    
    # Add all other sections in their original order
    for section_name, section_content in other_sections:
        if section_name != '_HEADER_':  # Skip header placeholder
            new_content_lines.extend(section_content)
            new_content_lines.append('')  # Empty line after section
    
    # Join content and ensure exactly one newline at the end
    new_content = '\n'.join(new_content_lines).rstrip() + '\n'
    
    # Write the reorganized content back to the file
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"✓ Reorganized {filepath}")

def main():
    """Process all .int files in the current directory."""
    current_dir = Path('.')
    int_files = list(current_dir.glob('*.int'))
    
    if not int_files:
        print("No .int files found in the current directory.")
        return
    
    print(f"Found {len(int_files)} .int files to process:")
    for file in int_files:
        print(f"  - {file.name}")
    
    print("\nProcessing files...")
    
    for int_file in int_files:
        try:
            reorganize_int_file(int_file)
        except Exception as e:
            print(f"✗ Error processing {int_file}: {e}")
    
    print(f"\nCompleted processing {len(int_files)} files.")

if __name__ == '__main__':
    main()
