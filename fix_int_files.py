#!/usr/bin/env python3
"""
Script to reorganize .int files:
1. Ensure [LevelInfo0] section comes first
2. Ensure [LevelSummary] section comes second
3. Keep all other sections unchanged in their relative order
4. Ensure files end with exactly one newline
"""

import os
import re
from pathlib import Path

def reorganize_int_file(filepath):
    """Reorganize a single .int file according to the requirements."""
    print(f"Processing {filepath}...")
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Remove trailing whitespace and ensure we work with clean content
    content = content.rstrip()
    
    if not content:
        # Empty file, just ensure it ends with one newline
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write('\n')
        print(f"✓ Fixed empty file {filepath}")
        return
    
    # Parse sections
    lines = content.split('\n')
    sections = {}
    current_section_name = None
    current_section_lines = []
    section_order = []
    
    for line in lines:
        # Check if this line starts a new section
        section_match = re.match(r'^\[([^\]]+)\]$', line)
        if section_match:
            # Save the previous section if it exists
            if current_section_name is not None:
                sections[current_section_name] = current_section_lines
                if current_section_name not in section_order:
                    section_order.append(current_section_name)
            
            # Start new section
            current_section_name = section_match.group(1)
            current_section_lines = [line]
        else:
            # Add line to current section
            if current_section_name is not None:
                current_section_lines.append(line)
    
    # Don't forget the last section
    if current_section_name is not None:
        sections[current_section_name] = current_section_lines
        if current_section_name not in section_order:
            section_order.append(current_section_name)
    
    # Build new content with correct order
    new_content_lines = []
    
    # Add LevelInfo0 first (if it exists)
    if 'LevelInfo0' in sections:
        new_content_lines.extend(sections['LevelInfo0'])
        new_content_lines.append('')  # Empty line after section
    
    # Add LevelSummary second (if it exists)
    if 'LevelSummary' in sections:
        new_content_lines.extend(sections['LevelSummary'])
        new_content_lines.append('')  # Empty line after section
    
    # Add all other sections in their original order
    for section_name in section_order:
        if section_name not in ['LevelInfo0', 'LevelSummary']:
            new_content_lines.extend(sections[section_name])
            new_content_lines.append('')  # Empty line after section
    
    # Remove the last empty line and ensure exactly one newline at the end
    while new_content_lines and new_content_lines[-1] == '':
        new_content_lines.pop()
    
    new_content = '\n'.join(new_content_lines) + '\n'
    
    # Write the reorganized content back to the file
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"✓ Reorganized {filepath}")

def main():
    """Process all .int files in the current directory."""
    current_dir = Path('.')
    int_files = list(current_dir.glob('*.int'))
    
    if not int_files:
        print("No .int files found in the current directory.")
        return
    
    print(f"Found {len(int_files)} .int files to process:")
    for file in int_files:
        print(f"  - {file.name}")
    
    print("\nProcessing files...")
    
    for int_file in int_files:
        try:
            reorganize_int_file(int_file)
        except Exception as e:
            print(f"✗ Error processing {int_file}: {e}")
    
    print(f"\nCompleted processing {len(int_files)} files.")

if __name__ == '__main__':
    main()
