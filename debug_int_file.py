#!/usr/bin/env python3
"""
Debug script to see what's happening with the .int files
"""

import re

def debug_int_file(filepath):
    """Debug a single .int file to see its structure."""
    print(f"Debugging {filepath}...")
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"Raw content:\n{repr(content)}")
    
    # Parse sections
    lines = content.split('\n')
    sections = {}
    current_section_name = None
    current_section_lines = []
    section_order = []
    
    for i, line in enumerate(lines):
        print(f"Line {i+1}: {repr(line)}")
        # Check if this line starts a new section
        section_match = re.match(r'^\[([^\]]+)\]$', line)
        if section_match:
            print(f"  -> Found section: {section_match.group(1)}")
            # Save the previous section if it exists
            if current_section_name is not None:
                sections[current_section_name] = current_section_lines
                if current_section_name not in section_order:
                    section_order.append(current_section_name)
            
            # Start new section
            current_section_name = section_match.group(1)
            current_section_lines = [line]
        else:
            # Add line to current section
            if current_section_name is not None:
                current_section_lines.append(line)
    
    # Don't forget the last section
    if current_section_name is not None:
        sections[current_section_name] = current_section_lines
        if current_section_name not in section_order:
            section_order.append(current_section_name)
    
    print(f"\nSections found: {list(sections.keys())}")
    print(f"Section order: {section_order}")
    
    for section_name in section_order:
        print(f"\n[{section_name}]:")
        for line in sections[section_name]:
            print(f"  {repr(line)}")

if __name__ == '__main__':
    debug_int_file('CU_01.int')
